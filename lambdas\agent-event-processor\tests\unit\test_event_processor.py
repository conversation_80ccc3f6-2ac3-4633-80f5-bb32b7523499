"""Unit tests for EventProcessor."""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from src.agent_event_processor.models.events import AgentEvent, EventType
from src.agent_event_processor.services.event_processor import EventProcessor
from src.agent_event_processor.config.settings import Settings


class TestEventProcessor:
    """Test cases for EventProcessor class."""

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        from src.agent_event_processor.config.settings import DatabaseSettings

        settings = Mock(spec=Settings)
        settings.database = DatabaseSettings()  # No parameters needed, uses environment variables
        settings.aws = Mock()
        settings.aws.region = 'us-east-1'
        return settings

    @pytest.fixture
    def mock_db_service(self):
        """Mock database service."""
        return Mock()

    @pytest.fixture
    def mock_dimension_manager(self):
        """Mock dimension manager."""
        return Mock()

    @pytest.fixture
    def mock_fact_manager(self):
        """Mock fact manager."""
        return Mock()

    @pytest.fixture
    def event_processor(
        self, mock_settings, mock_db_service, mock_dimension_manager, mock_fact_manager
    ):
        """Create EventProcessor instance with mocked dependencies."""
        db_service_path = (
            "src.agent_event_processor.services.event_processor.DatabaseService"
        )
        dim_manager_path = (
            "src.agent_event_processor.services.database_service.DimensionManager"
        )
        fact_manager_path = (
            "src.agent_event_processor.services.database_service.FactManager"
        )

        with (
            patch(db_service_path, return_value=mock_db_service),
            patch(dim_manager_path, return_value=mock_dimension_manager),
            patch(fact_manager_path, return_value=mock_fact_manager),
        ):
            return EventProcessor(mock_settings)

    @pytest.fixture
    def sample_login_event(self):
        """Sample login event for testing."""
        return AgentEvent(
            timestamp=datetime(2025, 2, 5, 11, 44, 18),
            eventType=EventType.LOGIN,
            agencyOrElement="Brandon911",
            agent="test-agent-001",
            event_data={
                "mediaLabel": "_ML_194D5ECDE50C0001C46A@BrandonMB",
                "uri": "tel:+2045553006",
                "agentRole": "Rural - CT",
                "reason": "normal",
                "operatorId": "OP001",
                "workstation": "WS-001",
                "deviceName": "Headset",
            },
        )

    def test_repository_dimension_keys(
        self, event_processor, sample_login_event
    ):
        """Test dimension key resolution through repository."""
        # Setup mocks for repository methods
        event_processor.repository.get_or_create_tenant_key = Mock(return_value=1)
        event_processor.repository.get_or_create_agent_key = Mock(return_value=2)
        event_processor.repository.get_or_create_date_key = Mock(return_value=20250205)
        event_processor.repository.get_or_create_time_key = Mock(return_value=114418)

        # Execute individual repository calls
        tenant_key = event_processor.repository.get_or_create_tenant_key(sample_login_event.agency_or_element)
        agent_key = event_processor.repository.get_or_create_agent_key(sample_login_event.agent, tenant_key)

        # Verify
        assert tenant_key == 1
        assert agent_key == 2
        event_processor.repository.get_or_create_tenant_key.assert_called_once_with(sample_login_event.agency_or_element)
        event_processor.repository.get_or_create_agent_key.assert_called_once_with(sample_login_event.agent, tenant_key)

    def test_timestamp_parsing(self, event_processor, sample_login_event):
        """Test timestamp parsing functionality."""
        # Test with datetime object
        dt_result = event_processor._parse_event_timestamp(sample_login_event.timestamp)
        assert isinstance(dt_result, datetime)

        # Test with string timestamp
        str_timestamp = "2025-02-05T11:44:18.031Z"
        str_result = event_processor._parse_event_timestamp(str_timestamp)
        assert isinstance(str_result, datetime)

        # Test timezone conversion
        local_time = event_processor._convert_to_local_time(str_result, "America/New_York")
        assert isinstance(local_time, datetime)
        assert local_time.tzinfo is not None



    def test_process_single_event_success(self, event_processor, sample_login_event):
        """Test successful single event processing."""
        # Setup
        mock_context = Mock()

        # Mock repository methods
        event_processor.repository.get_or_create_tenant_key = Mock(return_value=1)
        event_processor.repository.get_or_create_agent_key = Mock(return_value=2)
        event_processor.repository.get_or_create_date_key = Mock(return_value=20250205)
        event_processor.repository.get_or_create_time_key = Mock(return_value=114418)
        event_processor.repository.insert_agent_event = Mock()

        # Execute
        result = event_processor.process_single_event(sample_login_event, mock_context)

        # Verify
        assert result is True
        event_processor.repository.get_or_create_tenant_key.assert_called_once()
        event_processor.repository.get_or_create_agent_key.assert_called_once()
        event_processor.repository.insert_agent_event.assert_called_once()

    def test_process_single_event_failure(self, event_processor, sample_login_event):
        """Test single event processing failure."""
        # Setup
        mock_context = Mock()

        # Mock repository to raise exception
        event_processor.repository.get_or_create_tenant_key = Mock(
            side_effect=Exception("Database error")
        )

        # Execute & Verify
        result = event_processor.process_single_event(sample_login_event, mock_context)
        assert result is False
