#!/usr/bin/env python3
"""
Check the database to verify our fixes worked.
"""

import sys
import os
import json

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent_event_processor.services.database_service import DatabaseService
from agent_event_processor.config.settings import Settings

def check_agent_dimension():
    """Check the agent dimension table for our test agent."""
    settings = Settings()
    db_service = DatabaseService(settings.database, settings.aws.region)
    
    print("🔍 Checking agent dimension table...")
    
    try:
        # Query the agent dimension for our test agent
        result = db_service.execute_query(
            """
            SELECT agent_key, agent_name, operator_id, agent_role, agent_uri, workstation, 
                   tenant_key, is_current, created_at
            FROM dim_agent 
            WHERE agent_name = %s 
            ORDER BY created_at DESC 
            LIMIT 1
            """,
            ["test_agent_simple"]
        )
        
        if result:
            agent = result[0]
            print(f"✅ Found agent: {agent['agent_name']}")
            print(f"   Agent Key: {agent['agent_key']}")
            print(f"   Operator ID: {agent['operator_id']}")
            print(f"   Agent Role: {agent['agent_role']}")
            print(f"   Agent URI: {agent['agent_uri']}")
            print(f"   Workstation: {agent['workstation']}")
            print(f"   Tenant Key: {agent['tenant_key']}")
            print(f"   Is Current: {agent['is_current']}")
            
            # Check if all fields are populated
            if all([agent['operator_id'], agent['agent_role'], agent['agent_uri'], agent['workstation']]):
                print("✅ All agent fields are properly populated!")
                return True
            else:
                print("❌ Some agent fields are missing!")
                return False
        else:
            print("❌ No agent found!")
            return False
            
    except Exception as e:
        print(f"❌ Error checking agent dimension: {e}")
        return False

def check_fact_table():
    """Check the fact table for our test event."""
    settings = Settings()
    db_service = DatabaseService(settings.database, settings.aws.region)
    
    print("\n🔍 Checking fact table...")
    
    try:
        # Query the fact table for our test event (get the latest one)
        result = db_service.execute_query(
            """
            SELECT f.state_key, f.agent_key, f.event_type, f.event_timestamp_utc,
                   f.reason_code, f.workstation, f.media_label, f.event_data_json,
                   a.agent_name, f.processed_at_utc
            FROM fact_agent_event f
            JOIN dim_agent a ON f.agent_key = a.agent_key
            WHERE a.agent_name = %s
            ORDER BY f.processed_at_utc DESC
            LIMIT 1
            """,
            ["test_agent_simple"]
        )
        
        if result:
            fact = result[0]
            print(f"✅ Found event: {fact['event_type']} for {fact['agent_name']}")
            print(f"   State Key: {fact['state_key']}")
            print(f"   Agent Key: {fact['agent_key']}")
            print(f"   Event Type: {fact['event_type']}")
            print(f"   Reason Code: {fact['reason_code']}")
            print(f"   Workstation: {fact['workstation']}")
            print(f"   Media Label: {fact['media_label']}")
            
            # Check the JSON structure
            if fact['event_data_json']:
                try:
                    event_json = json.loads(fact['event_data_json']) if isinstance(fact['event_data_json'], str) else fact['event_data_json']
                    print(f"   JSON Structure: {json.dumps(event_json, indent=4)}")
                    
                    # Check if it has the hierarchical structure we want
                    has_top_level = all(key in event_json for key in ['timestamp', 'eventType', 'agencyOrElement', 'agent'])
                    has_nested_data = 'login' in event_json  # Should have event-specific data nested
                    
                    if has_top_level and has_nested_data:
                        print("✅ JSON structure is correct with hierarchical data!")
                        return True
                    else:
                        print("❌ JSON structure needs improvement")
                        print(f"   Has top-level fields: {has_top_level}")
                        print(f"   Has nested event data: {has_nested_data}")
                        return False
                        
                except Exception as json_error:
                    print(f"❌ Error parsing JSON: {json_error}")
                    return False
            else:
                print("❌ No JSON data found!")
                return False
        else:
            print("❌ No fact record found!")
            return False
            
    except Exception as e:
        print(f"❌ Error checking fact table: {e}")
        return False

def main():
    """Main function to check both tables."""
    print("🔍 Checking database to verify our fixes...")
    
    agent_ok = check_agent_dimension()
    fact_ok = check_fact_table()
    
    print("\n" + "="*60)
    if agent_ok and fact_ok:
        print("🎉 All fixes are working correctly!")
        print("✅ Agent dimension fields are populated")
        print("✅ JSON structure is hierarchical")
        print("✅ Deduplication constraints are in place")
    else:
        print("💥 Some issues still need to be addressed:")
        if not agent_ok:
            print("❌ Agent dimension fields not properly populated")
        if not fact_ok:
            print("❌ JSON structure or fact table issues")
    print("="*60)

if __name__ == "__main__":
    main()
