"""
Event Processor for Agent Events.

This module processes agent events and stores them in Redshift using the database service.
"""

from typing import Any, Dict, Optional
from datetime import datetime
import pytz

from aws_lambda_powertools import Logger

from src.agent_event_processor.config.settings import Settings
from src.agent_event_processor.models.events import AgentEvent
from src.agent_event_processor.services.database_service import DatabaseService
from src.agent_event_processor.services.database_repository import AgentEventRepository
from src.agent_event_processor.utils.decorators import capture_method
from src.agent_event_processor.utils.timezone_utils import get_client_timezone


logger = Logger()


class EventProcessor:
    """Simplified event processor for agent events."""

    def __init__(self, settings: Settings):
        """Initialize event processor."""
        self.settings = settings
        self.db_service = DatabaseService(settings.database, settings.aws.region)
        self.repository = AgentEventRepository(self.db_service)

        logger.info("Event processor initialized")

    @capture_method
    def process_single_event(self, event: AgentEvent, context: Any) -> bool:
        """Process a single agent event."""
        try:
            logger.info(
                "Processing agent event",
                event_type=event.event_type.value,
                agent=event.agent,
                agency=event.agency_or_element,
            )

            # Get dimension keys using repository
            tenant_key = self.repository.get_or_create_tenant_key(
                event.agency_or_element
            )

            # Extract agent data from event for dimension creation
            agent_data = self._extract_agent_data(event)
            agent_key = self.repository.get_or_create_agent_key(agent_data, tenant_key)

            # Parse event timestamp
            event_datetime = self._parse_event_timestamp(event.timestamp)
            date_key = self.repository.get_date_key(event_datetime)
            time_key = self.repository.get_time_key(event_datetime)

            # Extract business key fields for duplicate detection
            media_label = self._extract_field_value(event.event_data, "mediaLabel")
            workstation = self._extract_field_value(event.event_data, "workstation")
            reason_code = self._extract_field_value(event.event_data, "reason")

            # Prepare event data for insertion
            event_data = {
                "timestamp": event.timestamp,
                "event_type": event.event_type.value,
                "agencyOrElement": event.agency_or_element,
                "agent": event.agent,
                "event_data_json": event.event_data,
                "agent_key": agent_key,
                "tenant_key": tenant_key,
                "date_key": date_key,
                "time_key": time_key,
                "event_timestamp_utc": event_datetime,
                "event_timestamp_local": self._convert_to_local_time(
                    event_datetime, get_client_timezone()
                ),
                "media_label": media_label,
                "workstation": workstation,
                "reason_code": reason_code,
            }

            # Insert using repository
            self.repository.insert_agent_event(event_data)

            logger.info(
                "Agent event processed successfully",
                event_type=event.event_type.value,
                agent=event.agent,
                agent_key=agent_key,
                tenant_key=tenant_key,
            )
            return True

        except Exception as e:
            logger.error(
                "Event processing failed",
                event_type=event.event_type.value,
                agent=event.agent,
                agency=event.agency_or_element,
                error=str(e),
                exception=e,
            )
            return False

    def _parse_event_timestamp(self, timestamp_input: Any) -> datetime:
        """Parse event timestamp to datetime object."""
        # If it's already a datetime object, return it
        if isinstance(timestamp_input, datetime):
            return timestamp_input

        # If it's a string, parse it
        timestamp_str = str(timestamp_input)

        # Handle ISO format with Z suffix
        if timestamp_str.endswith("Z"):
            timestamp_str = timestamp_str[:-1] + "+00:00"

        # Parse the timestamp
        try:
            return datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
        except ValueError:
            # Fallback parsing
            parsed = datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S.%fZ")
            return parsed.replace(tzinfo=pytz.UTC)

    def _extract_agent_data(self, event: AgentEvent) -> Dict[str, Any]:
        """Extract agent data from event for dimension creation."""
        agent_data = {
            "agent_name": event.agent,
            "operator_id": self._extract_field_value(event.event_data, "operatorId"),
            "agent_role": self._extract_field_value(event.event_data, "agentRole"),
            "agent_uri": self._extract_field_value(event.event_data, "agentUri"),
            "workstation": self._extract_field_value(event.event_data, "workstation"),
        }
        return agent_data

    def _extract_field_value(self, event_data: dict, field_name: str) -> Optional[str]:
        """Extract field value from nested event data structure."""
        if not event_data:
            return None

        # Look through all nested dictionaries for the field
        for value in event_data.values():
            if isinstance(value, dict) and field_name in value:
                return value[field_name]

        return None

    def _convert_to_local_time(
        self, utc_datetime: datetime, timezone_name: str
    ) -> datetime:
        """Convert UTC datetime to local timezone."""
        if utc_datetime.tzinfo is None:
            utc_datetime = utc_datetime.replace(tzinfo=pytz.UTC)

        local_tz = pytz.timezone(timezone_name)
        return utc_datetime.astimezone(local_tz)
