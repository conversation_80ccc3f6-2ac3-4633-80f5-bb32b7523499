#!/usr/bin/env python3
"""
Test for all event types to ensure proper data population.
"""

import json
import sys
import os

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent_event_processor.lambda_function import lambda_handler

def create_test_event(event_type, agent_name, additional_data=None):
    """Create a test SQS event with the specified event type."""
    base_xml = f"""<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2025-02-05T11:44:18.031Z</timestamp>
    <eventType>{event_type}</eventType>
    <agencyOrElement>TestAgency</agencyOrElement>
    <agent>{agent_name}</agent>
    <reason>normal</reason>
    <workstation>TEST_WS_{event_type}</workstation>
    <operatorId>TEST_OP_{agent_name}</operatorId>
    <agentRole>Test Agent</agentRole>
    <agentUri>tel:+1234567890</agentUri>
    <mediaLabel>TEST_MEDIA_{event_type}</mediaLabel>
    <tenantGroup>TestAgency</tenantGroup>
    <deviceName>TestDevice_{event_type}</deviceName>"""

    if additional_data:
        for key, value in additional_data.items():
            base_xml += f"\n    <{key}>{value}</{key}>"

    base_xml += """
</LogEvent>"""

    return {
        "Records": [
            {
                "messageId": f"test-{event_type.lower()}-{agent_name}",
                "receiptHandle": f"test-receipt-{event_type.lower()}",
                "body": base_xml,
                "attributes": {
                    "ApproximateReceiveCount": "1",
                    "SentTimestamp": "1612526658031",
                    "SenderId": "AIDAIENQZJOLO23YVJ4VO",
                    "ApproximateFirstReceiveTimestamp": "1612526658031"
                },
                "messageAttributes": {},
                "md5OfBody": "test-md5",
                "eventSource": "aws:sqs",
                "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
                "awsRegion": "us-east-1"
            }
        ]
    }

def test_event_type(event_type, agent_name, additional_data=None):
    """Test a specific event type and return results."""
    print(f"\n{'='*60}")
    print(f"Testing {event_type} Event (Agent: {agent_name})")
    print(f"{'='*60}")
    
    # Create test event
    test_event = create_test_event(event_type, agent_name, additional_data)
    
    # Mock context
    class MockContext:
        def __init__(self):
            self.function_name = "test-function"
            self.function_version = "$LATEST"
            self.invoked_function_arn = "arn:aws:lambda:us-east-1:123456789012:function:test-function"
            self.memory_limit_in_mb = "256"
            self.aws_request_id = "test-request-id"

        def get_remaining_time_in_millis(self):
            return 300000
    
    context = MockContext()
    
    try:
        # Execute the Lambda
        response = lambda_handler(test_event, context)
        
        # Parse response
        body = json.loads(response['body'])
        successful = body.get('successful_count', 0)
        failed = body.get('failed_count', 0)
        
        if successful > 0 and failed == 0:
            print(f"{event_type} Event test PASSED!")
            print(f"Results: {successful} successful, {failed} failed")
            return True
        else:
            print(f"{event_type} Event test FAILED!")
            print(f"Results: {successful} successful, {failed} failed")
            if 'batchItemFailures' in response:
                print(f"Batch failures: {len(response['batchItemFailures'])} items")
            return False
            
    except Exception as e:
        print(f"{event_type} Event test FAILED with exception: {e}")
        return False

def main():
    """Run comprehensive tests for all event types."""
    print("Agent Event Processor - Comprehensive Event Type Testing")
    print("=" * 70)
    print("Testing all event types against REAL dev database")
    print("This will insert actual records into the dev database!")
    print("=" * 70)
    
    # Define test cases for different event types (using correct EventType enum values)
    test_cases = [
        ("Login", "test_agent_login", None),
        ("Logout", "test_agent_logout", None),
        ("ACDLogin", "test_agent_acd_login", {
            "acdId": "TEST_ACD_001",
            "ringGroupName": "TEST_RING_GROUP"
        }),
        ("ACDLogout", "test_agent_acd_logout", {
            "acdId": "TEST_ACD_002",
            "ringGroupName": "TEST_RING_GROUP_2"
        }),
        ("AgentBusiedOut", "test_agent_busied", {
            "busiedOutAction": "Manual",
            "busiedOutDuration": "300"
        }),
        ("AgentAvailable", "test_agent_available", {
            "busiedOutAction": "Manual"
        }),
    ]
    
    results = []
    
    for event_type, agent_name, additional_data in test_cases:
        success = test_event_type(event_type, agent_name, additional_data)
        results.append((event_type, success))
    
    # Summary
    print(f"\n{'='*70}")
    print("Test Summary:")
    print(f"{'='*70}")
    
    passed = 0
    failed = 0
    
    for event_type, success in results:
        status = "PASSED" if success else "FAILED"
        print(f"{event_type}: {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\n{'='*70}")
    if failed == 0:
        print(f"ALL {len(results)} TESTS PASSED!")
        print("All event types are working correctly with dev database")
        print("Records were successfully inserted into dev database")
    else:
        print(f"{failed} out of {len(results)} tests failed")
        print("Check the error logs above for details")
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
