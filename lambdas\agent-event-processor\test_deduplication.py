#!/usr/bin/env python3
"""
Test deduplication by running the same event twice.
"""

import json
import sys
import os

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent_event_processor.lambda_function import lambda_handler

def test_deduplication():
    """Test that the same event doesn't get inserted twice."""
    xml_body = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2025-02-05T11:44:18.031Z</timestamp>
    <eventType>Login</eventType>
    <agencyOrElement>TestAgency</agencyOrElement>
    <agent>test_agent_dedup</agent>
    <reason>normal</reason>
    <workstation>TEST_WS_Dedup</workstation>
    <operatorId>TEST_OP_DEDUP</operatorId>
    <agentRole>Test Agent Dedup</agentRole>
    <agentUri>tel:+1234567890</agentUri>
    <mediaLabel>TEST_MEDIA_DEDUP</mediaLabel>
    <tenantGroup>TestAgency</tenantGroup>
    <deviceName>TestDevice_Dedup</deviceName>
</LogEvent>"""

    test_event = {
        "Records": [{
            "messageId": "test-dedup-1",
            "receiptHandle": "test-receipt-1",
            "body": xml_body,
            "attributes": {},
            "messageAttributes": {},
            "md5OfBody": "test-md5",
            "eventSource": "aws:sqs",
            "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
            "awsRegion": "us-east-1"
        }]
    }

    class MockContext:
        def __init__(self):
            self.function_name = "test-function"
            self.function_version = "$LATEST"
            self.invoked_function_arn = "arn:aws:lambda:us-east-1:123456789012:function:test-function"
            self.memory_limit_in_mb = "256"
            self.aws_request_id = "test-request-id"

        def get_remaining_time_in_millis(self):
            return 300000

    context = MockContext()
    
    print("🔄 Testing deduplication...")
    
    # First insertion
    print("\n1️⃣ First insertion:")
    try:
        response1 = lambda_handler(test_event, context)
        body1 = json.loads(response1['body'])
        print(f"   Result: {body1.get('successful_count', 0)} successful, {body1.get('failed_count', 0)} failed")
        first_success = body1.get('successful_count', 0) > 0 and body1.get('failed_count', 0) == 0
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        first_success = False
    
    # Second insertion (should be deduplicated)
    print("\n2️⃣ Second insertion (should be deduplicated):")
    try:
        response2 = lambda_handler(test_event, context)
        body2 = json.loads(response2['body'])
        print(f"   Result: {body2.get('successful_count', 0)} successful, {body2.get('failed_count', 0)} failed")
        # For deduplication, we expect it to still be "successful" but not create a duplicate
        second_success = body2.get('successful_count', 0) > 0 and body2.get('failed_count', 0) == 0
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        second_success = False
    
    if first_success and second_success:
        print("\n✅ Deduplication test PASSED!")
        print("   Both events processed successfully (duplicate was handled gracefully)")
        return True
    else:
        print("\n❌ Deduplication test FAILED!")
        return False

if __name__ == "__main__":
    print("Testing deduplication logic...")
    success = test_deduplication()
    if success:
        print("\n🎉 Deduplication is working correctly!")
    else:
        print("\n💥 Deduplication needs to be fixed.")
