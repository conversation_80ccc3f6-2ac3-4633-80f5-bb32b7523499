"""
Database Repository Pattern for Agent Event Processing.

This module provides a cleaner, more scalable approach to database operations
using a repository pattern instead of complex manager classes.
"""

from typing import Dict, Any, Optional
from datetime import datetime

from aws_lambda_powertools import Logger

from src.agent_event_processor.services.database_service import (
    DatabaseService,
    DimensionManager,
    FactManager,
)
from src.agent_event_processor.utils.timezone_utils import get_client_timezone


logger = Logger()


class AgentEventRepository:
    """Repository for agent event database operations."""

    def __init__(self, db_service: DatabaseService):
        """Initialize repository with database service and managers."""
        self.db = db_service
        self.dimension_manager = DimensionManager(db_service)
        self.fact_manager = FactManager(db_service)

    def get_or_create_tenant_key(self, tenant_name: str) -> int:
        """Get or create tenant key using dimension manager."""
        timezone_name = get_client_timezone()
        return self.dimension_manager.get_or_create_tenant_key(
            tenant_name, timezone_name
        )

    def get_or_create_agent_key(self, agent_data: Dict[str, Any], tenant_key: int) -> int:
        """Get or create agent key"""
        return self.dimension_manager.get_or_create_agent_key(agent_data, tenant_key)

    def get_date_key(self, event_date: datetime) -> int:
        """Get date key in YYYYMMDD format."""
        return self.dimension_manager.get_date_key(event_date)

    def get_time_key(self, event_time: datetime) -> int:
        """Get time key in HHMMSS format."""
        return self.dimension_manager.get_time_key(event_time)

    def get_or_create_queue_key(
        self, queue_name: Optional[str], tenant_key: int
    ) -> Optional[int]:
        """Get or create queue key - not implemented yet."""
        return None

    def insert_agent_event(self, event_data: Dict[str, Any]) -> None:
        """Insert agent event using proper dimension and fact managers."""

        # Get tenant key
        tenant_key = self.get_or_create_tenant_key(event_data["agencyOrElement"])

        # Prepare agent data for dimension manager
        agent_data = {
            "agent_name": event_data["agent"],
            "operator_id": event_data.get("event_data", {}).get("operatorId"),
            "agent_role": event_data.get("event_data", {}).get("agentRole"),
            "agent_uri": event_data.get("event_data", {}).get("agentUri"),
            "workstation": event_data.get("event_data", {}).get("workstation"),
        }

        # Get agent key
        agent_key = self.get_or_create_agent_key(agent_data, tenant_key)

        # Get date and time keys
        event_timestamp = event_data["event_timestamp_utc"]
        date_key = self.get_date_key(event_timestamp)
        time_key = self.get_time_key(event_timestamp)

        # Prepare complete event data for JSON storage
        complete_event_data = {
            "timestamp": (
                event_data["timestamp"]
                if isinstance(event_data["timestamp"], str)
                else event_data["timestamp"].isoformat()
            ),
            "eventType": event_data["event_type"],  # Use event_type from processor
            "agencyOrElement": event_data["agencyOrElement"],
            "agent": event_data["agent"],
            **event_data.get("event_data", {}),
        }

        # Prepare fact data for the fact manager
        fact_data = {
            "agent_key": agent_key,
            "tenant_key": tenant_key,
            "date_key": date_key,
            "time_key": time_key,
            "event_timestamp_utc": event_data["event_timestamp_utc"],
            "event_timestamp_local": event_data["event_timestamp_local"],
            "event_type": event_data["event_type"],
            "reason_code": event_data.get("event_data", {}).get("reason", None),
            "workstation": event_data.get("event_data", {}).get("workstation", None),
            "event_data_json": complete_event_data,
        }

        # Insert using fact manager
        success = self.fact_manager.insert_agent_event(fact_data)

        if not success:
            raise Exception(f"Failed to insert agent event for {event_data['agent']}")

        logger.info(
            "Agent event inserted successfully",
            event_type=event_data["event_type"],
            agent_key=agent_key,
            tenant_key=tenant_key,
        )
