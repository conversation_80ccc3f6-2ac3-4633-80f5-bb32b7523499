#!/usr/bin/env python3
"""
Simple test to verify our fixes work.
"""

import json
import sys
import os

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent_event_processor.lambda_function import lambda_handler

def test_simple_login():
    """Test a simple login event."""
    xml_body = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2025-02-05T11:44:18.031Z</timestamp>
    <eventType>Login</eventType>
    <agencyOrElement>TestAgency</agencyOrElement>
    <agent>test_agent_simple</agent>
    <reason>normal</reason>
    <workstation>TEST_WS_Login</workstation>
    <operatorId>TEST_OP_001</operatorId>
    <agentRole>Test Agent</agentRole>
    <agentUri>tel:+1234567890</agentUri>
    <mediaLabel>TEST_MEDIA_Login</mediaLabel>
    <tenantGroup>TestAgency</tenantGroup>
    <deviceName>TestDevice_Login</deviceName>
</LogEvent>"""

    test_event = {
        "Records": [{
            "messageId": "test-login-simple",
            "receiptHandle": "test-receipt",
            "body": xml_body,
            "attributes": {},
            "messageAttributes": {},
            "md5OfBody": "test-md5",
            "eventSource": "aws:sqs",
            "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
            "awsRegion": "us-east-1"
        }]
    }

    class MockContext:
        def __init__(self):
            self.function_name = "test-function"
            self.function_version = "$LATEST"
            self.invoked_function_arn = "arn:aws:lambda:us-east-1:123456789012:function:test-function"
            self.memory_limit_in_mb = "256"
            self.aws_request_id = "test-request-id"

        def get_remaining_time_in_millis(self):
            return 300000

    context = MockContext()
    
    try:
        response = lambda_handler(test_event, context)
        print("Response:", json.dumps(response, indent=2))
        
        body = json.loads(response['body'])
        successful = body.get('successful_count', 0)
        failed = body.get('failed_count', 0)
        
        if successful > 0 and failed == 0:
            print("✅ Test PASSED!")
            return True
        else:
            print("❌ Test FAILED!")
            print(f"Results: {successful} successful, {failed} failed")
            return False
            
    except Exception as e:
        print(f"❌ Test FAILED with exception: {e}")
        return False

if __name__ == "__main__":
    print("Testing simple login event with our fixes...")
    success = test_simple_login()
    if success:
        print("\n🎉 All fixes are working correctly!")
    else:
        print("\n💥 There are still issues to fix.")
